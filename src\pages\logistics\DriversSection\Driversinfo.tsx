import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import { User, Mail, Phone, FileText, MapPin, Truck } from "lucide-react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { OutlinedButton, PrimaryButton } from "@/components/custom/buttons/buttons";
import { DefaultBadge, PrimaryBadge } from "@/components/custom/badges/badges";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { useGetSiteVisitsQuery, useGetVehicleRequestsQuery } from "@/redux/slices/logistics";
import { format } from "date-fns";

interface DriverProfileModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  driverData: {
    id: string;
    name: string;
    email: string;
    phoneNumber: string;
    imageUrl: string;
    license: string;
    licenseNumber: string;
    status: string;
    joinDate?: string;
    employee_no: string;
  };
}

const DriverProfileModal: React.FC<DriverProfileModalProps> = ({
  isOpen,
  onOpenChange,
  driverData,
}) => {
  const [activeTab, setActiveTab] = useState<"details" | "trips" | "chart">("details");

  // Fetch site visits for this driver using employee_no - only completed ones
  const { data: siteVisitsResponse } = useGetSiteVisitsQuery({
    page: 1,
    page_size: 100,
    driver: driverData.employee_no, // Use employee_no to filter site visits
    status: "Completed", // Only fetch completed site visits
  });

  // Fetch vehicle requests for this driver - only completed ones
  const { data: vehicleRequestsResponse } = useGetVehicleRequestsQuery({
    page: 1,
    page_size: 100,
    driver: driverData.employee_no, // Use employee_no to filter vehicle requests
    status: "Trip Completed", // Only fetch completed vehicle requests
  });

  const siteVisits = siteVisitsResponse?.data?.results || [];
  const vehicleRequests = vehicleRequestsResponse?.data?.results || [];

  // Transform site visits into trips data
  const siteVisitTrips = siteVisits.filter(visit => visit && visit.pickup_date).map((visit) => ({
    id: `sv-${visit.id}`,
    date: visit.pickup_date ? format(new Date(visit.pickup_date), "MMM dd, yyyy") : "N/A",
    destination: visit.pickup_location || "Unknown location",
    status: visit.status || "Unknown",
    vehicle: typeof visit.vehicle === 'object' && visit.vehicle !== null
      ? (visit.vehicle as any)?.vehicle_registration || (visit.vehicle as any)?.make || "Vehicle assigned"
      : visit.vehicle || "Not assigned",
    distance: "N/A", // Distance not available in API
    type: "Site Visit",
  }));

  // Transform vehicle requests into trips data
  const vehicleRequestTrips = vehicleRequests.filter(request => request && request.pickup_date).map((request) => ({
    id: `vr-${request.id}`,
    date: request.pickup_date ? format(new Date(request.pickup_date), "MMM dd, yyyy") : "N/A",
    destination: `${request.pickup_location || "Unknown"} → ${request.destination_location || "Unknown"}`,
    status: request.status || "Unknown",
    vehicle: typeof request.vehicle === 'object' && request.vehicle !== null
      ? (request.vehicle as any)?.vehicle_registration || (request.vehicle as any)?.make || "Vehicle assigned"
      : request.vehicle || "Not assigned",
    distance: "N/A", // Distance not available in API
    type: "Vehicle Request",
  }));

  // Combine both types of trips
  const trips = [...siteVisitTrips, ...vehicleRequestTrips].sort((a, b) => {
    try {
      if (a.date === "N/A" || b.date === "N/A") return 0;
      return new Date(b.date).getTime() - new Date(a.date).getTime();
    } catch (error) {
      console.warn("Error sorting trips:", error);
      return 0;
    }
  });

  // Prepare chart data - group by month and count trips
  const chartData = trips.filter(trip => trip && trip.date && trip.date !== "N/A").reduce((acc: { name: string; value: number }[], trip) => {
    try {
      const month = format(new Date(trip.date), "MMMM");
      const existingMonth = acc.find((item) => item.name === month);

      if (existingMonth) {
        existingMonth.value += 1;
      } else {
        acc.push({ name: month, value: 1 });
      }
    } catch (error) {
      console.warn("Error processing trip date:", trip.date, error);
    }

    return acc;
  }, []).sort((a, b) => {
    // Sort months chronologically
    const months = ["January", "February", "March", "April", "May", "June",
                   "July", "August", "September", "October", "November", "December"];
    return months.indexOf(a.name) - months.indexOf(b.name);
  });

  return (
    <BaseModal
      isOpen={isOpen}
      onOpenChange={onOpenChange}
      title={`Driver: ${driverData.name}`}
      description="View driver profile and past trips"
      size="2xl"
      footer={
        <div className="flex justify-end w-full">
          <PrimaryButton onClick={() => onOpenChange(false)}>Close</PrimaryButton>
        </div>
      }
    >
      <div className="py-2">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="bg-blue-100 rounded-full p-3">
              <User className="h-6 w-6 text-blue-600" />
            </div>
            <div>
              <h3 className="font-medium">{driverData.employee_no}</h3>
              <p className="text-sm text-gray-500">
                Joined {driverData.joinDate || "April 2024"}
              </p>
            </div>
          </div>
          <PrimaryBadge
            className={
              driverData.status === "Active"
                ? "bg-green-100 text-green-800 hover:bg-green-100"
                : "bg-gray-100 text-gray-800 hover:bg-green-100"
            }
          >
            {driverData.status}
          </PrimaryBadge>
        </div>

        <Tabs
          defaultValue="details"
          value={activeTab}
          onValueChange={(value) => setActiveTab(value as "details" | "trips" | "chart")}
          className="w-full"
        >
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="trips">Trips Made</TabsTrigger>
            <TabsTrigger value="chart">Trips Chart</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">Email:</span>
                <span className="text-sm">{driverData.email}</span>
              </div>
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">Phone:</span>
                <span className="text-sm">{driverData.phoneNumber}</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">License Type:</span>
                <span className="text-sm">{driverData.license}</span>
              </div>
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium">License Number:</span>
                <span className="text-sm">{driverData.licenseNumber}</span>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="trips">
            <div className="space-y-3">
              {trips.length > 0 ? (
                trips.map((trip) => (
                  <div
                    key={trip.id}
                    className="border rounded-md p-3 flex justify-between items-center"
                  >
                    <div>
                      <h4 className="font-medium">{trip.destination}</h4>
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <span>{trip.date}</span>
                        <span>•</span>
                        <span>{trip.type}</span>
                        <span>•</span>
                        <span>{trip.vehicle}</span>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-1">
                      <DefaultBadge className="bg-green-100 text-green-800 hover:bg-green-100">
                        {trip.status}
                      </DefaultBadge>
                      <span className="text-xs text-gray-400">{trip.type}</span>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-sm text-gray-500">No past trips found.</p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="chart">
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Trips Per Month</h4>
              {chartData.length > 0 ? (
                <div className="w-full h-64">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart
                      data={chartData}
                      margin={{
                        top: 5,
                        right: 30,
                        left: 20,
                        bottom: 5,
                      }}
                    >
                      <CartesianGrid strokeDasharray="3 3" className="opacity-20" />
                      <XAxis
                        dataKey="name"
                        axisLine={false}
                        tick={{ fontSize: 12 }}
                      />
                      <YAxis
                        axisLine={false}
                        tick={{ fontSize: 12 }}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: 'hsl(var(--background))',
                          border: '1px solid hsl(var(--border))',
                          borderRadius: '6px',
                        }}
                      />
                      <Bar
                        dataKey="value"
                        fill="hsl(var(--primary))"
                        radius={[4, 4, 0, 0]}
                        animationDuration={1500}
                      />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              ) : (
                <p className="text-sm text-gray-500">No trip data available for chart.</p>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </BaseModal>
  );
};

export default DriverProfileModal;