import BaseModal from "@/components/custom/modals/BaseModal";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useEffect, useState } from "react";
import { IconSwitch } from "@/components/custom/switch/IconSwitch";
import { Label } from "@/components/ui/label";
import Multiselect from "@/components/custom/forms/Multiselect";
import {
  useBookPlotMutation,
  useGetReserveDiasporaPlotQuery,
} from "@/redux/slices/projects";
import { toast } from "sonner";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";
import { useAuthHook } from "@/utils/useAuthHook";
import { FileText, Image } from "lucide-react";

type Props = {
  setSbModal: (e: boolean) => void;
  sbModal: boolean;
  bookingType: string;
  title: string;
};

const formSchema = z.object({
  client_name: z
    .string()
    .min(1, { message: "Customer full name is required" })
    .trim(),
  client_phone: z
    .string()
    .min(1, { message: "Customer phone number is required" })
    .trim(),
  email: z.string().min(0),
  transaction_id: z.string().min(0),
});

const DiaporaBookingModal = ({
  setSbModal,
  sbModal,
  bookingType,
  title,
}: Props) => {
  const { user_details } = useAuthHook();
  const [bookSite, { isLoading: booking }] = useBookPlotMutation();
  const { data: reservedPlots, isLoading: gettingPlots } =
    useGetReserveDiasporaPlotQuery({ marketer: user_details?.employee_no });
  const [selectedPlots, setSelectedPlots] = useState<{
    label: string;
    value: string;
  }>();
  const [plotsOptions, setPlotOptions] = useState<
    { value: any; label: string }[]
  >([]);
  // const [proof_of_payment, setProof_of_payment] = useState();
  const [file, setFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isPdf, setIsPdf] = useState(false);

  const handleFileChange = (e: any) => {
    const selected = e.target.files[0];
    if (selected) {
      setFile(selected);
      setIsPdf(selected.type === "application/pdf");

      if (selected.type.startsWith("image/")) {
        setPreviewUrl(URL.createObjectURL(selected));
      } else {
        setPreviewUrl(null);
      }
    } else {
      setFile(null);
      setPreviewUrl(null);
      setIsPdf(false);
    }
  };

  useEffect(() => {
    if (reservedPlots?.data?.results?.length > 0) {
      let options: any = [];
      reservedPlots?.data?.results?.map((ls: any) => {
        let plots_list = ls?.plots?.split(",");
        plots_list?.map((pl: any) => {
          let option = { label: pl, value: pl };
          options.push(option);
        });
      });
      setPlotOptions(options);
    }
  }, [gettingPlots]);

  // Define your form.
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      client_name: "",
      client_phone: "",
      email: "",
      transaction_id: "",
    },
  });

  // submit handler.
  async function onSubmit(values: z.infer<typeof formSchema>): Promise<void> {
    if (!selectedPlots) {
      toast.error("Please select at least one plot");
      return;
    }
    // const tags = selectedPlots?.map((plot) => plot.value);
    const tags = [selectedPlots.value];

    const formData = {
      ...values,
      // diaspora_reservation:
      proof_of_payment: file,
      plots: tags,
      booking_type: bookingType,
    };

    try {
      const newFormData = new FormData();
      Object.entries(formData).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            // value.forEach((item) => newFormData.append(key, item));
            newFormData.append(key, JSON.stringify(value));
          } else if (value instanceof Date) {
            newFormData.append(key, value.toISOString());
          } else {
            newFormData.append(key, value as string | Blob);
          }
        }
      });
      const res = await bookSite(newFormData).unwrap();
      if (res) {
        toast.success("Booking created successfully");
        setSbModal(false);
      }
    } catch (error: any) {
      console.log("Error: ", error);
      if (error?.data) {
        toast.error(`${error?.data?.error}`);
      } else {
        toast.error(`${error?.error}`);
      }
      return;
    }
  }

  return (
    <BaseModal
      isOpen={sbModal}
      onOpenChange={setSbModal}
      size="lg"
      title={title}
      description="Fields with (*) are required"
      // footer={<Button onClick={() => setSbModal(false)}>Close</Button>}
    >
      <div className="py-4">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="marketer">Select plot from your reserved*</Label>
              <Multiselect
                value={selectedPlots}
                data={plotsOptions}
                setValue={setSelectedPlots}
                loading={gettingPlots}
                isClearable={true}
                isDisabled={false}
                isMultiple={false}
                isSearchable={true}
              />
            </div>

            <FormField
              control={form.control}
              name="client_name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Name*</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter Full Name"
                      className="border border-accent  dark:border-white/40"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="client_phone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Customer Phone Number*</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter Phone Number"
                      className="border border-accent  dark:border-white/40"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="transaction_id"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Booking Fee Mpesa ID*</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Enter Mpesa Transaction ID"
                      className="border border-accent  dark:border-white/40"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormItem>
                  <FormLabel>Attach a Proof of Payment document*</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="proof of payment"
                      type="file"
                      className="border border-accent  dark:border-white/40"
                      onChange={(e) => field.onChange(e.target.files?.[0])}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem> */}
            <div className="mx-auto ">
              <FormLabel>Attach Proof of Payment document*</FormLabel>
              <label
                htmlFor="file-upload"
                className="flex flex-col items-center justify-center border-2 border-dashed border-gray-300 rounded-2xl cursor-pointer p-6 text-center hover:border-blue-500 transition duration-300"
              >
                <input
                  id="file-upload"
                  type="file"
                  accept="image/*,application/pdf"
                  onChange={handleFileChange}
                  className="hidden"
                />

                {previewUrl && !isPdf ? (
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="w-full h-64 object-contain rounded-xl shadow-md"
                  />
                ) : file && isPdf ? (
                  <div className="flex flex-col items-center">
                    <FileText className="h-12 w-12 text-gray-400 mb-2" />
                    <p className="text-gray-700 font-medium">PDF Selected</p>
                  </div>
                ) : (
                  <>
                    <Image className="h-12 w-12 text-gray-400 mb-2" />
                    <p className="text-gray-500">
                      Click to upload image or PDF
                    </p>
                  </>
                )}
              </label>

              {file && (
                <p className="mt-2 text-center text-sm text-gray-600">
                  Selected file:{" "}
                  <span className="font-medium">{file.name}</span>
                </p>
              )}
            </div>

            <div className="w-full flex justify-end">
              {booking ? (
                <SpinnerTemp type="spinner-double" size="sm" />
              ) : (
                <Button type="submit" className="justify-end">
                  Submit
                </Button>
              )}
            </div>
          </form>
        </Form>
      </div>
    </BaseModal>
  );
};

export default DiaporaBookingModal;
