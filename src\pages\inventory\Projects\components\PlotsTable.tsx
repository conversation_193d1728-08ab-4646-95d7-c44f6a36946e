import { DataTable } from "@/components/custom/tables/Table1";
import { But<PERSON> } from "@/components/ui/button";
import { plotTypes } from "@/types/project";
import { ColumnDef } from "@tanstack/react-table";
import BookingsModal from "../modals/BookingsModal";
import { useEffect, useState } from "react";
import DropdownButton from "@/components/custom/Dropdowns/dropdown";
import { Ticket, TicketMinus, Tickets } from "lucide-react";
import BaseModal from "@/components/custom/modals/BaseModal";
import { OutlinedButton } from "@/components/custom/buttons/buttons";
import { addComma } from "@/utils/helpers";
import useCurrentCurrencyStore from "@/zustand/useCurrentCurrencyStore";
import { useGetForexQuery, useGetPlotsQuery } from "@/redux/slices/projects";
import handleCurrencyConvertion from "@/utils/currencyConvert";
import SpinnerTemp from "@/components/custom/spinners/SpinnerTemp";

type Props = {
  projectId: string;
  plotStatus?: string;
};

const PlotsTable = ({ projectId, plotStatus }: Props) => {
  const { data: forexData, isLoading: loading } = useGetForexQuery({});
  const currentCurrency = useCurrentCurrencyStore((state) => state.currency);
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  console.log("plotStatus", plotStatus);

  const { data: plots, isLoading: plotsLoading } = useGetPlotsQuery({
    project: projectId,
    search: searchValue,
    page: currentPage,
    page_size: itemsPerPage,
    plot_status: plotStatus,
  });

  const [sbModal, setSbModal] = useState(false);
  const [mpModal, setMpModal] = useState(false);
  const [obModal, setObModal] = useState(false);
  const [isBookOptionsModalOpen, setIsBookOptionsModalOpen] = useState(false);
  const [openBookOptions, setOpenBookOptions] = useState<string | undefined>(
    ""
  );

  const handleViewBookOptions = (plotId: string) => {
    setOpenBookOptions(plotId);
    setIsBookOptionsModalOpen(true);
  };

  const columns: ColumnDef<plotTypes>[] = [
    {
      accessorKey: "plot_no",
      header: "Plot NO.",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "plot_size",
      header: "Size",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
    },
    {
      accessorKey: "cash_price",
      header: "Price",
      cell: (info) => (
        <div className="flex">
          {currentCurrency}{" "}
          {handleCurrencyConvertion(
            String(info?.getValue() ?? "0"),
            currentCurrency,
            forexData?.results?.[0]
          )}
        </div>
      ),
      enableColumnFilter: false,
    },

    {
      accessorKey: "action",
      header: "status/Action",
      cell: ({ row }) => {
        const a = row.original;
        return (
          <div className="flex items-center gap-2">
            <span
              className={`${
                a?.plot_status === "Open"
                  ? "bg-primary text-white"
                  : a?.plot_status === "Reserved"
                  ? "bg-yellow-400 text-white"
                  : "bg-destructive text-white"
              } text-center px-2 rounded-full`}
            >
              {a?.plot_status}
            </span>
            {a?.plot_status === "Open" && (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleViewBookOptions(a?.plot_no ?? "")}
                >
                  Book Plot
                </Button>
                {/* 
          <Button variant="default" size="sm" className="text-sm">
            Book
          </Button> */}
              </>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];

  return (
    <div>
      {plotsLoading ? (
        <div className="w-full flex items-center justify-center">
          <SpinnerTemp type="spinner-double" size="md" />
        </div>
      ) : (
        <DataTable<plotTypes>
          data={plots?.data?.results || []}
          columns={columns}
          title="Plots"
          enableToolbar={true}
          enableExportToExcel={true}
          enablePagination={true}
          enablePrintPdf={false}
          enableFullScreenToggle={false}
          enableColumnControl={false}
          enableColumnFilters={true}
          enableSorting={true}
          enableSelectColumn={true}
          enableSelectToolbar={false}
          enableSelectToolbarButtonExportToExcel={true}
          enableSelectToolbarButtonPrintToPdf={true}
          containerClassName=""
          tableClassName=""
          tHeadClassName="bg-secondary"
          tHeadCellsClassName="text-primary"
          tBodyClassName=""
          tBodyTrClassName=""
          tBodyCellsClassName="md:!p-2 !py-1"
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={plots?.data?.total_data || 0}
          searchInput={
            <input
              value={searchValue}
              name="searchValue"
              type="search"
              onChange={(e) => setSearchValue(e.target.value)}
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search prospects..."
            />
          }
        />
      )}

      <BookingsModal
        sbModal={sbModal}
        setSbModal={setSbModal}
        bookingType="SPECIAL"
        title="Special Booking"
        b_plot={openBookOptions}
      />
      <BookingsModal
        sbModal={mpModal}
        setSbModal={setMpModal}
        bookingType="MPESA"
        title="Mpesa Booking"
        b_plot={openBookOptions}
      />
      <BookingsModal
        sbModal={obModal}
        setSbModal={setObModal}
        bookingType="OTHER"
        title="Other Booking"
        b_plot={openBookOptions}
      />

      {isBookOptionsModalOpen && openBookOptions && (
        <BaseModal
          size="md"
          isOpen={isBookOptionsModalOpen}
          onOpenChange={() => {
            setOpenBookOptions(undefined);
            setIsBookOptionsModalOpen(false);
          }}
          title="Book Plot"
          description={`Plot Number: ${openBookOptions}`}
        >
          <div className="flex lg:flex-col sm:flex-row flex-wrap gap-3">
            <OutlinedButton
              variant="primary"
              // className="dark:bg-black/10 dark:border-white/40 dark:text-white/80"
              onClick={() => setSbModal(true)}
            >
              Special Bookings
            </OutlinedButton>
            <OutlinedButton variant="primary" onClick={() => setMpModal(true)}>
              M-Pesa Bookings
            </OutlinedButton>
            <OutlinedButton variant="primary" onClick={() => setObModal(true)}>
              Other Bookings
            </OutlinedButton>
          </div>
        </BaseModal>
      )}
    </div>
  );
};

export default PlotsTable;
