import { apiSlice } from "../apiSlice";
import { getSalesApiParams } from "@/utils/salesPermissions";
import { RootState } from "../store";

export const projectsApiSlice = apiSlice.injectEndpoints({
    endpoints: (builder) => ({
        getAllSales: builder.query({
            query: (params) => ({ getState }) => {
                const state = getState() as RootState;
                const userDetails = state.auth.user_details;
                const filteredParams = getSalesApiParams(userDetails, params);

                // If user has no access, return empty result
                if (filteredParams.no_access) {
                    return {
                        url: "/sales-views/",
                        method: "GET",
                        params: { page_size: 0 }, // This will return empty results
                    };
                }

                return {
                    url: "/sales-views/",
                    method: "GET",
                    params: filteredParams,
                };
            },
            providesTags: ["Sales"],
        }),
        getSaleDetails: builder.query({
            query: (saleId) => ({ getState }) => {
                const state = getState() as RootState;
                const userDetails = state.auth.user_details;
                const filteredParams = getSalesApiParams(userDetails);

                // If user has no access, return 404-like response
                if (filteredParams.no_access) {
                    return {
                        url: "/sales-views/no-access",
                        method: "GET",
                    };
                }

                return {
                    url: `/sales-views/${saleId}`,
                    method: "GET",
                    params: filteredParams,
                };
            },
            providesTags: ["Sales"],
        }),
        getOngoingSales: builder.query({
            query: (params) => ({ getState }) => {
                const state = getState() as RootState;
                const userDetails = state.auth.user_details;
                const filteredParams = getSalesApiParams(userDetails, { ...params, status: 'ongoing' });

                if (filteredParams.no_access) {
                    return {
                        url: "/sales-views/",
                        method: "GET",
                        params: { page_size: 0 },
                    };
                }

                return {
                    url: "/sales-views/",
                    method: "GET",
                    params: filteredParams,
                };
            },
            providesTags: ["Sales"],
        }),
        getCompletedSales: builder.query({
            query: (params) => ({ getState }) => {
                const state = getState() as RootState;
                const userDetails = state.auth.user_details;
                const filteredParams = getSalesApiParams(userDetails, { ...params, status: 'completed' });

                if (filteredParams.no_access) {
                    return {
                        url: "/sales-views/",
                        method: "GET",
                        params: { page_size: 0 },
                    };
                }

                return {
                    url: "/sales-views/",
                    method: "GET",
                    params: filteredParams,
                };
            },
            providesTags: ["Sales"],
        }),
    })
});

export const { 
    useGetAllSalesQuery, 
    useGetSaleDetailsQuery, 
    useGetCompletedSalesQuery, 
    useGetOngoingSalesQuery 
} = projectsApiSlice;
