import { Screen } from "@/app-components/layout/screen";
import { PrimaryButton } from "@/components/custom/buttons/buttons";
import BaseModal from "@/components/custom/modals/BaseModal";
import { DataTable } from "@/components/custom/tables/Table1";
import { ColumnDef } from "@tanstack/react-table";
import { CheckCheck, Recycle, RefreshCcw, Tag, User } from "lucide-react";
import React, { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import {
  useGetPlotBookingsQuery,
  useUpdatePlotBookingMutation,
} from "@/redux/slices/projects";
import { formatDateTime } from "@/utils/formatDate";
import { toast } from "sonner";
import InputWithTags from "@/components/custom/forms/InputWithTags";

interface Booking {
  booking_id: string;
  booking_type: string;
  plots: string;
  amount: string;
  marketer_name: string;
  customer_name: string;
  transaction_id: string;
  creation_date: string;
  proof_of_payment: string;
  upload_time: string;
  type: string;
  status: string;
}

export default function AllBookings() {
  const [searchValue, setSearchValue] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20);
  const { data: bookingsData, isLoading: loading } = useGetPlotBookingsQuery({
    status: "DONE",
  });

  // Column definitions for TanStack React Table
  const columns: ColumnDef<Booking>[] = [
    {
      accessorKey: "Client",
      header: "Client Name",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex items-center gap-3">
            <User size={26} className="text-primary" />
            <div className="flex flex-col gap-1">
              <p>
                <strong>Marketer: </strong> {rowData.marketer_name}{" "}
              </p>
              <p>
                <strong>Client: </strong> {rowData.customer_name}{" "}
              </p>
              <p>
                <strong>Plot(s) No: </strong> {rowData.plots}{" "}
              </p>
            </div>
          </div>
        );
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "mpesaTransaction",
      header: "Mpesa",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex items-center gap-3">
            <Tag className="text-primary" />
            <div className="flex flex-col gap-1">
              <p>
                <strong>Amount Paid: </strong> {rowData.amount}
              </p>
              {rowData.booking_type == "MPESA" && (
                <p>
                  <strong>Mpesa Transactions: </strong>{" "}
                  {rowData.transaction_id || "Not Paid"}
                </p>
              )}
              <p>
                <strong>Date: </strong> {formatDateTime(rowData.creation_date)}
              </p>
            </div>
          </div>
        );
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "proof_of_payment",
      header: "Attachments",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex flex-col gap-1">
            {rowData?.proof_of_payment ? (
              <>
                <p>
                  <strong>Upload Date:</strong>{" "}
                  {rowData.upload_time || "No Upload"}{" "}
                </p>
                <a
                  href={rowData.proof_of_payment}
                  className="text-blue-600 underline"
                >
                  Open attached file
                </a>
              </>
            ) : (
              <p className="text-destructive">No File Uploaded</p>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "booking_type",
      header: "Type",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: (info) => info.getValue(),
      enableColumnFilter: false,
      filterFn: "includesString",
    },
    {
      accessorKey: "actions",
      header: "Actions",
      cell: (info) => {
        const rowData = info.row.original;
        return (
          <div className="flex gap-4">
            <div title="Send offer letter">
              <SendOfferLetter />
            </div>
            <div title="Revert plot units">
              <Revert booking_id={rowData?.booking_id} />
            </div>
            <div title="Swap plot units">
              <Swap booking_id={rowData?.booking_id} plots={rowData?.plots} />
            </div>
          </div>
        );
      },
      enableColumnFilter: false,
      enableSorting: false,
    },
  ];

  return (
    <Screen>
      <div className=" !m-0 min-h-screen ">
        <div className="border p-3 mb-2 rounded">
          <h2 className=" font-bold text-lg">All Bookings</h2>
        </div>
        <DataTable<Booking>
          data={bookingsData?.data?.results || []}
          columns={columns}
          title="All Booking"
          enableSelectColumn={false}
          enableColumnFilters={true}
          enableSorting={true}
          enableToolbar={true}
          enableFullScreenToggle={true}
          enableColumnControl={true}
          tableClassName="border"
          containerClassName=" rounded py-2"
          tBodyCellsClassName="text-xs"
          tHeadClassName="bg-secondary"
          enablePagination={true}
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          itemsPerPage={itemsPerPage}
          setItemsPerPage={setItemsPerPage}
          totalItems={bookingsData?.data?.total_data || 0}
          searchInput={
            <input
              value={searchValue}
              name="searchValue"
              type="search"
              onChange={(e) => setSearchValue(e.target.value)}
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="Search prospects..."
            />
          }
        />
      </div>
    </Screen>
  );
}

function SendOfferLetter() {
  const navigate = useNavigate();

  const handleSendOfferLetter = () => {
    // Navigate to the offer letter page
    navigate("/offer-letter");
  };

  return (
    <CheckCheck
      size={21}
      className="text-primary cursor-pointer hover:text-primary/80"
      onClick={handleSendOfferLetter}
    />
  );
}

function Revert({ booking_id }: { booking_id: string }) {
  const [updateBooking, { isLoading: approving }] =
    useUpdatePlotBookingMutation();
  const [isModelXOpen, setIsModelXOpen] = React.useState(false);
  const [reason, setReason] = React.useState("");

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!reason) {
      toast.error("Please provide a reason for reverting the booking");
      return;
    }
    let newFormData = { reason, id: booking_id, action: "revert" };
    try {
      const res = await updateBooking(newFormData).unwrap();
      if (res?.booking_id) {
        toast.success("Booking reverted successfully");
        setIsModelXOpen(false);
        setReason("");
      } else {
        toast.error("Failed to revert booking:");
        return;
      }
    } catch (error) {
      console.log("error", error);
      toast.error("Something went wrong, please try again");
      return;
    }
  }

  return (
    <>
      <Recycle
        size={21}
        className="text-primary"
        onClick={() => setIsModelXOpen(true)}
      />

      <BaseModal
        className="!p-0"
        size="lg"
        isOpen={isModelXOpen}
        onOpenChange={setIsModelXOpen}
        title="Reason for reverting"
        headerClassName="px-4 pt-5"
      >
        <div className="px-4 pt-2 pb-10 dark:bg-black/40">
          <form onSubmit={onSubmit} className="space-y-3">
            <textarea
              value={reason}
              name="reason"
              rows={3}
              onChange={(e) => setReason(e.target.value)}
              className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              placeholder="type in your reason..."
            ></textarea>

            <PrimaryButton
              type={approving ? "button" : "submit"}
              className="w-full !mt-6"
            >
              {approving ? "Reverting..." : "Submit"}
            </PrimaryButton>
          </form>
        </div>
      </BaseModal>
    </>
  );
}

function Swap({ booking_id, plots }: { booking_id: string; plots?: string }) {
  const [isModelXOpen, setIsModelXOpen] = React.useState(false);

  const [updateBooking, { isLoading: approving }] =
    useUpdatePlotBookingMutation();
  const [tags, setTags] = useState<string[]>([]);

  async function onSubmit(e: React.FormEvent) {
    e.preventDefault();

    if (tags.length === 0) {
      toast.error("Please enter at least one plot number to swap");
      return;
    }

    let newFormData = {
      id: booking_id,
      action: "swap",
      swap_to_plot_number: tags,
    };
    try {
      const res = await updateBooking(newFormData).unwrap();
      if (res?.booking_id) {
        toast.success("Booking swap successfully");
        setIsModelXOpen(false);
      } else {
        toast.error("Failed to revert booking:");
        return;
      }
    } catch (error: any) {
      console.log("error", error);
      if (error?.data?.error) {
        toast.error(error.data.error);
      } else {
        toast.error("Something went wrong, please try again");
      }
      return;
    }
  }
  return (
    <>
      <RefreshCcw
        size={21}
        className="text-primary"
        onClick={() => setIsModelXOpen(true)}
      />

      <BaseModal
        className="!p-0"
        size="lg"
        isOpen={isModelXOpen}
        onOpenChange={setIsModelXOpen}
        title="Swap plot units"
        headerClassName="px-4 pt-4"
      >
        <div className="px-4  pb-10 dark:bg-black/40">
          <div className="border-l-[10px] border-primary bg-primary/10 px-5 py-5 text-xs rounded">
            <p>
              This action will revert the changes done on{" "}
              <strong>Plot(s) {plots}</strong> and make the entered plot number
              reserved.
            </p>
            <p>
              All payments and attached documents will be moved to the new plot
              number.
            </p>
          </div>
          <div className="h-6"></div>
          <form onSubmit={onSubmit} className="space-y-3">
            <div className="space-y-2">
              <div className="flex gap-2 items-center flex-wrap">
                <label>Plot Number(s) *</label>
                <small>
                  <small className="text-destructive">
                    ( Use <b>comma</b> to separate your plot numbers or press{" "}
                    <b>Enter</b> )
                  </small>
                </small>
              </div>
              <InputWithTags tags={tags} setTags={setTags} />
            </div>

            <PrimaryButton
              type={approving ? "button" : "submit"}
              className="w-full !mt-6"
            >
              {approving ? "swapping" : "Submit"}
            </PrimaryButton>
          </form>
        </div>
      </BaseModal>
    </>
  );
}

interface SearchComponentProps {
  universalSearchValue: string;
  setuniversalSearchValue: React.Dispatch<React.SetStateAction<string>>;
}

function SearchComponent({
  universalSearchValue,
  setuniversalSearchValue,
}: SearchComponentProps) {
  return (
    <input
      value={universalSearchValue}
      onChange={(e) => setuniversalSearchValue(e.target.value)}
      className="px-3 py-2 ml-1 w-full border rounded text-sm focus:outline-none"
      placeholder="Search..."
    />
  );
}
