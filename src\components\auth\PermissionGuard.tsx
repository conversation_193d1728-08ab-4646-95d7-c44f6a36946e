import React from 'react';
import { useLocation } from 'react-router-dom';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';
import UnauthorizedPage from '@/pages/UnauthorizedPage';

// Route to permission mapping
const ROUTE_PERMISSIONS: Record<string, number[]> = {
  // Main section routes (permission 111)
  '/': [111],

  // Logistics routes with specific permissions
  '/logistics-dash': [205], // Access Logistics Dashboard
  '/logistics': [207], // Access Clients
  '/logistics/vehicles': [209], // Access Vehicles
  '/drivers': [208], // Access Drivers
  '/logistics-stats': [206], // Access Logistics Statistics
  '/logistics-reports': [111], // General access for now
  '/book-visit': [201], // Book Visit
  '/all-visits': [111], // General access for viewing all visits
  '/logistics/site-visits': [111], // for dynamic routes like /logistics/site-visits/:id
  '/logistics/cashback': [111],
  '/logistics/transactions': [111],
  '/clients': [207], // Access Clients
  '/allsitevisitreport': [111],
  
  // Sales routes (permission 111 - Main section)
  '/sales': [111],
  '/sales/overview': [111],
  '/new-sales': [111],
  
  // Customers routes (permission 111 - Main section)
  '/customers': [111],
  '/customers/overview': [111],
  '/customer': [111], // for dynamic routes like /customer/:id
  
  // Prospects routes (permission 111 - Main section)
  '/prospects': [111],
  '/prospect': [111], // for dynamic routes like /prospect/:id
  
  // Inventory routes (permission 111 - Main section)
  '/inventory': [111],
  '/projects': [111],
  '/project': [111], // for dynamic routes like /project/:id
  '/mybookings': [111],
  '/inventory/accounts': [111],
  '/inventory/booking-approvals': [111],
  '/diaspora-trips': [111],
  '/diaspora-reservations': [111],
  '/diaspora-receipts': [111],
  '/inventory/pricing': [111],
  '/mpesa-transactions': [111],
  '/inventory/logs': [111],
  
  // Performance routes (permission 112)
  '/performance': [112],
  '/office-performance': [112],
  '/targets': [112],
  '/commissions': [112],
  '/cash-on-cash': [112],
  '/team-performance': [112],
  '/portfolio': [112],
  '/profiles': [112],
  
  // Teams routes (permission 113)
  '/accounts-dashboard': [113],
  '/gm-karen-dashboard': [113],
  '/hq-hos-dashboard': [113],
  '/diaspora-dashboard': [113],
  '/credits-team-dashboard': [113],
  '/marketer-dashboard': [113],
  '/hr-team': [113],
  '/legal-team': [113],
  '/data-team': [113],
  '/tele-marketing-team': [113],
  '/digital-team': [113],
  
  // Reports routes (permission 114)
  '/marketerreport': [114],
  '/plotmarketreport': [114],
  '/diasporareports': [114],
  '/money-in-reports': [114],
  '/installment-reports': [114],
  '/project-summary': [114],
  
  // Analytics routes (permission 115)
  '/money-in-analytics': [115],
  '/installment-analytics': [115],
  
  // Services routes (permission 116)
  '/admin/services': [116],
  '/complaints': [116],
  '/feedback': [116],
  '/ticketing': [116],
  '/flags': [116],
  '/engagements': [116],
  '/notifications': [116],
  '/notes': [116],
  '/reminders': [116],
  '/forms': [116],
  '/surveys': [116],
  
  // Admin routes (permission 117)
  '/users-list': [117],
  '/admin/teams': [117],
  '/admin/groups': [117],
  '/permissions': [117],
  '/admin/assign-role': [117],
  '/groups': [117],
  '/admin/departments': [117],
  
  // Profile routes - accessible to all authenticated users
  '/profile': [], // No specific permission required, just authentication

  // System routes - accessible to all authenticated users
  '/unauthorized': [], // No specific permission required, just authentication
};

interface PermissionGuardProps {
  children: React.ReactNode;
  requiredPermissions?: number[];
  fallbackComponent?: React.ComponentType;
}

const PermissionGuard: React.FC<PermissionGuardProps> = ({
  children,
  requiredPermissions,
  fallbackComponent: FallbackComponent = UnauthorizedPage,
}) => {
  const location = useLocation();
  const { hasPermission, isLoading } = useSidebarPermissions();

  // Show loading state while permissions are being fetched
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Determine required permissions for the current route
  let permissions = requiredPermissions;
  
  if (!permissions) {
    const currentPath = location.pathname;
    
    // Check for exact match first
    permissions = ROUTE_PERMISSIONS[currentPath];
    
    // If no exact match, check for partial matches (for dynamic routes)
    if (!permissions) {
      for (const [route, perms] of Object.entries(ROUTE_PERMISSIONS)) {
        if (currentPath.startsWith(route) && route !== '/') {
          permissions = perms;
          break;
        }
      }
    }
    
    // Default to Main section permission if no specific permission found
    if (!permissions) {
      permissions = [111]; // Main section permission
    }
  }

  // If no permissions required (empty array), allow access
  if (permissions.length === 0) {
    return <>{children}</>;
  }

  // Check if user has any of the required permissions
  const hasAccess = permissions.some(permission => hasPermission(permission));

  if (!hasAccess) {
    return <FallbackComponent />;
  }

  return <>{children}</>;
};

export default PermissionGuard;
