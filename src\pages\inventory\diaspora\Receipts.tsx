import { useState } from "react";
import { Eye, Download, X } from "lucide-react";
import { ColumnDef } from "@tanstack/react-table";
import { DataTable } from "@/components/custom/tables/Table1";
import { Button } from "@/components/ui/button";
import { format } from "date-fns";
import { Screen } from "@/app-components/layout/screen";
import { useGetPlotBookingsQuery } from "@/redux/slices/projects";
import ReceiptDetailsModal from "./ReceiptDetailsModal";

export interface PlotBooking {
  id: string;
  plots: string;
  marketer: string;
  marketer_name: string;
  customer_name: string;
  customer: string;
  booking_type: string;
  booking_id: number;
  amount: string;
  transaction_id: string;
  transportation_transaction_id: string;
  description: string;
  office: string;
  proof_of_payment: string;
  upload_time: string;
  status: string;
  deadline: string;
  creation_date: string;
  expected_payment_date: string;
}

export default function Receipts() {
  const { data: bookings, isLoading } = useGetPlotBookingsQuery({
    booking_type: "OTHER",
  });
  console.log("boooikng diasp", bookings);

  const [selectedBooking, setSelectedBooking] = useState<PlotBooking | null>(
    null
  );
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSmallScreen, setIsSmallScreen] = useState(false);

  useState(() => {
    const checkScreenSize = () => {
      setIsSmallScreen(window.innerWidth < 768);
    };
    checkScreenSize();
    window.addEventListener("resize", checkScreenSize);
    return () => window.removeEventListener("resize", checkScreenSize);
  });

  const openDetailsModal = (booking: PlotBooking) => {
    setSelectedBooking(booking);
    setIsModalOpen(true);
  };

  const getMobileColumns = (): ColumnDef<PlotBooking>[] => [
    {
      accessorKey: "plots",
      header: "Plot(s)",
      cell: (info) => (
        <span className="font-medium">{info.getValue() as string}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "customer_name",
      header: "Client",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "amount",
      header: "Amount",
      cell: (info) => {
        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {(info.getValue() as number).toLocaleString()}
            </span>
            <span className="text-xs text-gray-500">
              {info.row.original.amount}
            </span>
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              openDetailsModal(booking);
            }}
            className="h-8 w-8 p-0"
          >
            <Eye className="h-4 w-4" />
            <span className="sr-only">View details</span>
          </Button>
        );
      },
      enableColumnFilter: false,
    },
  ];

  const getDesktopColumns = (): ColumnDef<PlotBooking>[] => [
    {
      accessorKey: "plots",
      header: "Plot(s)",
      cell: (info) => (
        <span className="font-medium">{info.getValue() as string}</span>
      ),
      enableColumnFilter: false,
    },
    {
      accessorKey: "marketer_name",
      header: "Marketer",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      accessorKey: "amount",
      header: "Amount Paid",
      cell: (info) => {
        const booking = info.row.original;
        return (
          <div className="flex flex-col">
            <span className="font-medium">
              {(info.getValue() as number).toLocaleString()}
            </span>
            <span className="text-xs text-gray-500">
              MPESA - {booking.transaction_id}
            </span>
            {booking.transportation_transaction_id && (
              <span className="text-xs text-gray-500">
                MPESA - {booking.transportation_transaction_id}
              </span>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "attachments",
      header: "Attachments",
      cell: (info) => {
        const booking = info.row.original;
        return (
          <div className="flex flex-col space-y-1">
            {booking.proof_of_payment && (
              <a
                href={booking.proof_of_payment}
                target="_blank"
                rel="noopener noreferrer"
                download
                className="text-blue-500 hover:text-blue-700 text-sm flex items-center"
              >
                <span className="material-icons text-blue-500 mr-1 text-xl">
                  &#x1F4CE;
                </span>
                Open Proof Of Payment
              </a>
            )}
          </div>
        );
      },
      enableColumnFilter: false,
    },
    {
      accessorKey: "deadline",
      header: "Expires On",
      cell: (info) =>
        format(new Date(info.getValue() as Date), "HH:mm:ss MM-dd-yyyy"),
      enableColumnFilter: false,
    },
    {
      accessorKey: "customer_name",
      header: "Client Name",
      cell: (info) => info.getValue() as string,
      enableColumnFilter: false,
    },
    {
      id: "actions",
      header: "Actions",
      cell: ({ row }) => {
        const booking = row.original;
        return (
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                openDetailsModal(booking);
              }}
              className="h-8 w-8 p-0"
            >
              <Eye className="h-4 w-4" />
              <span className="sr-only">View details</span>
            </Button>
            {/* <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                console.log("Download details for", booking.plots);
              }}
              className="h-8 w-8 p-0"
            >
              <Download className="h-4 w-4" />
              <span className="sr-only">Download</span>
            </Button> */}
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                console.log("Delete details for", booking.booking_id);
              }}
              className="text-red-500 hover:text-red-700 h-8 w-8 p-0"
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Delete</span>
            </Button>
          </div>
        );
      },
      enableColumnFilter: false,
    },
  ];
  const columns = isSmallScreen ? getMobileColumns() : getDesktopColumns();

  return (
    <Screen>
      <div className="space-y-6">
        <div className="bg-white rounded-lg shadow-sm border dark:bg-gray-800 dark:text-white">
          <div className="p-4 border-b">
            <h1 className="text-lg md:text-xl lg:text-2xl font-semibold text-gray-800 dark:text-white">
              {isSmallScreen
                ? "DIASPORA BOOKINGS"
                : "RECEIPTED DIASPORA SPECIAL BOOKINGS"}
            </h1>
          </div>
          <div className="p-2 md:p-4">
            <div className="overflow-x-auto">
              <DataTable<PlotBooking>
                data={bookings?.data ? bookings.data.results : []}
                columns={columns}
                enableToolbar={true}
                enableExportToExcel={!isSmallScreen}
                enablePrintPdf={!isSmallScreen}
                enablePagination={true}
                enableColumnFilters={!isSmallScreen}
                enableSorting={true}
                searchInput={<SearchComponent />}
                tHeadClassName="bg-secondary"
              />
            </div>
          </div>
        </div>
        <ReceiptDetailsModal
          isOpen={isModalOpen}
          onOpenChange={setIsModalOpen}
          selectedBooking={selectedBooking}
        />
      </div>
    </Screen>
  );
}

function SearchComponent() {
  const [universalSearchValue, setUniversalSearchValue] = useState("");
  const [isSmallScreen, setIsSmallScreen] = useState(false);
  return (
    <input
      value={universalSearchValue}
      onChange={(e) => setUniversalSearchValue(e.target.value)}
      className="px-4 py-2 w-full border rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
      placeholder={
        isSmallScreen ? "Search..." : "Search plots, clients, marketers..."
      }
    />
  );
}
