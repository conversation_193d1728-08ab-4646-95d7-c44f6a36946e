import React from 'react';
import { useLogisticsPermissions } from '@/hooks/useLogisticsPermissions';
import { useSidebarPermissions } from '@/hooks/useSidebarPermissions';
import { useAuthHook } from '@/utils/useAuthHook';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

/**
 * Debug component to display current user's logistics permissions
 * This component can be temporarily added to any page to debug logistics permission issues
 */
export const LogisticsPermissionDebugger: React.FC = () => {
  const { user_details } = useAuthHook();
  const {
    canBookVisit,
    canCompleteTrips,
    canCreateVehicleRequest,
    canCreateSpecialAssignment,
    canAccessLogisticsDashboard,
    canAccessLogisticsStatistics,
    canAccessClients,
    canAccessDrivers,
    canAccessVehicles,
    userLogisticsPermissions,
    userPermissionCodes,
    isLoading,
    LOGISTICS_PERMISSIONS,
  } = useLogisticsPermissions();

  const { userPermissionCodes: allPermissionCodes } = useSidebarPermissions();

  if (isLoading) {
    return (
      <Card className="fixed bottom-4 right-4 w-96 max-h-96 overflow-auto shadow-lg z-50">
        <CardHeader>
          <CardTitle className="text-sm">Logistics Permission Debugger</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-gray-600">Loading permissions...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="fixed bottom-4 right-4 w-96 max-h-96 overflow-auto shadow-lg z-50">
      <CardHeader>
        <CardTitle className="text-sm">Logistics Permission Debugger</CardTitle>
        <CardDescription className="text-xs">
          Debug logistics permissions for current user
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-3 text-xs">
        <div>
          <strong>User:</strong> {user_details?.fullnames || "Unknown"}
        </div>
        
        <div>
          <strong>Employee No:</strong> {user_details?.employee_no || "Unknown"}
        </div>
        
        <div>
          <strong>Department:</strong> {user_details?.department || "Unknown"}
        </div>

        <div>
          <strong>Team:</strong> {user_details?.team || "Unknown"}
        </div>
        
        <div>
          <strong>All Permission Codes:</strong>
          <div className="ml-2 flex flex-wrap gap-1 mt-1">
            {allPermissionCodes.length > 0 ? (
              allPermissionCodes.map(code => (
                <Badge 
                  key={code} 
                  variant={Object.values(LOGISTICS_PERMISSIONS).includes(code) ? "default" : "secondary"}
                  className="text-xs"
                >
                  {code}
                </Badge>
              ))
            ) : (
              <span className="text-red-600">No permissions found</span>
            )}
          </div>
        </div>

        <div>
          <strong>Logistics Permissions:</strong>
          <div className="ml-2 space-y-1">
            {Object.entries(LOGISTICS_PERMISSIONS).map(([key, code]) => {
              const hasPermission = userPermissionCodes.includes(code);
              return (
                <div key={key} className={`flex justify-between ${hasPermission ? 'text-green-600' : 'text-red-600'}`}>
                  <span>{key} ({code})</span>
                  <span>{hasPermission ? '✓' : '✗'}</span>
                </div>
              );
            })}
          </div>
        </div>

        <div>
          <strong>Specific Checks:</strong>
          <div className="ml-2 space-y-1">
            <div className={`flex justify-between ${canBookVisit ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Book Visit</span>
              <span>{canBookVisit ? '✓' : '✗'}</span>
            </div>
            <div className={`flex justify-between ${canCompleteTrips ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Complete Trips</span>
              <span>{canCompleteTrips ? '✓' : '✗'}</span>
            </div>
            <div className={`flex justify-between ${canCreateVehicleRequest ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Create Vehicle Request</span>
              <span>{canCreateVehicleRequest ? '✓' : '✗'}</span>
            </div>
            <div className={`flex justify-between ${canCreateSpecialAssignment ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Create Special Assignment</span>
              <span>{canCreateSpecialAssignment ? '✓' : '✗'}</span>
            </div>
            <div className={`flex justify-between ${canAccessLogisticsDashboard ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Access Dashboard</span>
              <span>{canAccessLogisticsDashboard ? '✓' : '✗'}</span>
            </div>
            <div className={`flex justify-between ${canAccessLogisticsStatistics ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Access Statistics</span>
              <span>{canAccessLogisticsStatistics ? '✓' : '✗'}</span>
            </div>
            <div className={`flex justify-between ${canAccessClients ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Access Clients</span>
              <span>{canAccessClients ? '✓' : '✗'}</span>
            </div>
            <div className={`flex justify-between ${canAccessDrivers ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Access Drivers</span>
              <span>{canAccessDrivers ? '✓' : '✗'}</span>
            </div>
            <div className={`flex justify-between ${canAccessVehicles ? 'text-green-600' : 'text-red-600'}`}>
              <span>Can Access Vehicles</span>
              <span>{canAccessVehicles ? '✓' : '✗'}</span>
            </div>
          </div>
        </div>

        <div>
          <strong>User Logistics Permissions:</strong>
          <div className="ml-2">
            {userLogisticsPermissions.length > 0 ? (
              userLogisticsPermissions.map(permission => (
                <Badge key={permission} variant="default" className="text-xs mr-1 mb-1">
                  {permission}
                </Badge>
              ))
            ) : (
              <span className="text-red-600">No logistics permissions</span>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LogisticsPermissionDebugger;
