import React, { useState } from "react";
import {
  Sidebar,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON>bar<PERSON>ooter,
  SidebarHeader,
} from "@/components/ui/sidebar";
import { TeamSwitcher } from "@/app-components/sidebar/team-switcher";
import { NavMain } from "@/app-components/sidebar/nav-main";
import { useTheme } from "@/hooks/use-theme";
import {
  useSidebarPermissions,
  SidebarPermissionKey,
} from "../../hooks/useSidebarPermissions";

import {
  Home,
  ShoppingBag,
  UserPlus,
  Users,
  Truck,
  Box,
  CreditCard,
  FileText,
  Repeat,
  Map,
  Key,
  UserCheck,
  GitGraph,
  BarChart2,
  Target,
  DollarSign,
  PieChart,
  Globe,
  PhoneCall,
  Cpu,
  TrendingUp,
  CalendarCheck,
  CheckSquare,
  AlertCircle,
  MessageCircle,
  Ticket,
  Flag,
  Bell,
  FileEdit,
  List,
  Clock,
  Search,
  Grid,
  User,
  Shield,
  Briefcase,
  Building,
  MapPin,
  GrapeIcon,
  Bus,
  Sun,
  Moon,
  WalletCards,
  CircuitBoard,
  BarChart,
  ChartNoAxesCombined,
  <PERSON><PERSON>ie,
  ChartColumnBig,
  ChartBarDecreasing,
  ChartBarStacked,
  ChartArea,
  Group,
  Users2,
  KeyRound,
  DollarSignIcon,
  Monitor,
} from "lucide-react";
import { NavUser } from "./nav-user";
import useNavStore from "@/zustand/useNavStore";

// Check if we're in development mode
const isDevelopment = import.meta.env.VITE_PROD === 'development';

const navData = {
  teams: [{ name: "Optiven Limited", logo: Cpu, plan: "Enterprise" }],
  navMain: [
    { title: "Dashboard", url: "/", icon: Home, isActive: false },
    {
      title: "Sales",
      url: "",
      icon: ShoppingBag,
      items: [
        { title: "All Sales", url: "/sales/overview#all-sales" },
        { title: "On Going Sales", url: "/sales/overview#on-going-sales" },
        { title: "Completed Sales", url: "/sales/overview#completed-sales" },
        { title: "Dropped Sales", url: "/sales/overview#dropped-sales" },
        ...(isDevelopment ? [{ title: "Report Demo", url: "/sales/report" }] : []),
      ],
    },
    { title: "Prospects", url: "/prospects", icon: GitGraph },
    {
      title: "Customers",
      url: "",
      icon: Users,
      items: [
        { title: "All Customers", url: "/customers/overview#all-customers" },
        // {
        //   title: "Active Customers",
        //   url: "/customers/overview#active-customers",
        // },
        // {
        //   title: "Completed Customers",
        //   url: "/customers/overview#completed-customers",
        // },
        // {
        //   title: "Dropped Customers",
        //   url: "/customers/overview#dropped-customers",
        // },
        // { title: "Report Demo", url: "/sales/report" },
      ],
    },

    {
      title: "Logistics",
      url: "/logistics",
      icon: Truck,
      items: [
        { title: "Dashboard", url: "/logistics-dash" },
        { title: "Logistics Stats", url: "/logistics-stats", icon: BarChart2 },
        { title: "Clients", url: "/clients", icon: Users },
        { title: "Drivers", url: "/drivers", icon: UserCheck },
        {
          title: "Vehicles",
          url: "/logistics/vehicles",
          icon: FileText,
          items: [
            {
              title: "Vehicle Details",
              url: "/logistics/vehicles#vehicle-details",
            },
            {
              title: "Request a Vehicle",
              url: "/logistics/vehicles#vehicle-requests",
            },
          ],
        },
        {
          title: "Logistics Reports",
          url: "/logistics-reports",
          icon: FileText,
        },
        {
          title: " Site Visit Reports",
          url: "/allsitevisitreport",
          icon: GrapeIcon,
        },
      ],
    },
    {
      title: "Inventory",
      url: "/inventory",
      icon: Box,
      items: [
        { title: "Dashboard", url: "/projects", icon: Home },
        {
          title: "My Booking",
          url: "",
          icon: FileText,
          items: [
            {
              title: "All Booking",
              url: "/mybookings#ALL",
              icon: Globe,
            },
            {
              title: "Special Booking",
              url: "/mybookings#SPECIAL",
              icon: CheckSquare,
            },
            {
              title: "Mpesa Booking",
              url: "/mybookings#MPESA",
              icon: DollarSign,
            },
            {
              title: "Other Booking",
              url: "/mybookings#OTHER",
              icon: Repeat,
            },
            {
              title: "Diaspora Booking",
              url: "/mybookings#DIASPORA",
              icon: AlertCircle,
            },
          ],
        },
        {
          title: "Special Bookings",
          url: "/inventory/booking-approvals",
          icon: Clock,
        },
        {
          title: "Accounts",
          url: "",
          icon: CreditCard,
          items: [
            {
              title: "All Bookings",
              url: "/inventory/accounts/all-bookings",
              icon: List,
            },
          ],
        },
        {
          title: "Diaspora",
          url: "",
          icon: Globe,
          items: [
            { title: "Diaspora Trips", url: "/diaspora-trips", icon: Map },
            {
              title: "Diaspora Reservations",
              url: "/diaspora-reservations",
              icon: Map,
            },
            {
              title: "Diaspora Receipts",
              url: "/diaspora-receipts",
              icon: FileText,
            },
          ],
        },
        {
          title: "Reports",
          url: "",
          icon: FileText,
          items: [
            {
              title: "Marketers Reports",
              url: "/plotmarketreport",
              icon: BarChart2,
            },
            { title: "Project Reports", url: "", icon: FileText },
          ],
        },
        {
          title: "Pricing",
          url: "",
          icon: DollarSign,
          items: [
            {
              title: "Project Pricing",
              url: "/inventory/pricing/project-pricing",
              icon: DollarSign,
            },
            {
              title: "Payment Plan Checker",
              url: "/inventory/pricing/payment-plan-checker",
              icon: CalendarCheck,
            },
          ],
        },
        {
          title: "Mpesa Transactions",
          url: "/mpesa-transactions",
          icon: CreditCard,
          items: [],
        },
        {
          title: "Inventory Logs",
          url: "/inventory/logs",
          icon: FileEdit,
          items: [],
        },
      ],
    },
    ...(isDevelopment ? [{
      title: "Cashback",
      url: "/logistics/cashback",
      icon: CreditCard,
      items: [],
    }] : []),
    ...(isDevelopment ? [{
      title: "Offer Letters",
      url: "/offer-letters",
      icon: FileText,
      items: [],
    }] : []),
    ...(isDevelopment ? [{ title: "Offer Letter", url: "/ol", icon: Cpu }] : []),
  ],

  NavPerformance: [
    { title: "Marketer Performance", url: "/performance", icon: BarChart2 },

    { title: "Office Performance", url: "/office-performance", icon: Cpu },
    { title: "Targets", url: "/targets", icon: Target },
    { title: "Commissions", url: "/commission report", icon: DollarSign },
    { title: "Marketer Targets", url: "/marketer-targets", icon: Target },
    {
      title: "Marketer Commissions",
      url: "/marketer-commissions",
      icon: DollarSign,
    },
    { title: "Cash On Cash", url: "/cash-on-cash", icon: PieChart },
    { title: "Team Performance", url: "/team-performance", icon: Users },
    { title: "Portfolio", url: "/portfolio", icon: Briefcase },
    { title: "Profiles", url: "/profiles", icon: User },
  ],

  navAdmin: [
    ...(isDevelopment ? [{
      title: "Users",
      url: "/admin/users",
      icon: Users,
      items: [
        { title: "Users List", url: "/users-list", icon: List },
        { title: "Users Grid", url: "/users-grid", icon: Grid },
        { title: "View Profile", url: "/profile", icon: User },
      ],
    }] : []),
    ...(isDevelopment ? [{
      title: "Role & Access",
      url: "/admin/roles",
      icon: Key,
      items: [
        { title: "Permissions", url: "/permissions", icon: Shield },
        { title: "Groups", url: "/groups", icon: Users },
      ],
    }] : []),
    { title: "Users List", url: "/users-list", icon: List },
    { title: "Teams", url: "/admin/teams", icon: Users2 },
    { title: "Groups", url: "/admin/groups", icon: Group },
    { title: "Permissions", url: "/permissions", icon: KeyRound },
    ...(isDevelopment ? [{ title: "Assign Role", url: "/admin/assign-role", icon: UserCheck }] : []),
    ...(isDevelopment ? [{ title: "Maps", url: "/admin/maps", icon: Map }] : []),
  ],

  NavTeams: [
    { title: "Accounts", url: "/accounts-dashboard", icon: ChartColumnBig },
    { title: "GM Karen", url: "/gm-karen-dashboard", icon: ChartPie },
    { title: "HQ HOS", url: "/hq-hos-dashboard", icon: ChartNoAxesCombined },
    { title: "Diaspora", url: "/diaspora-dashboard", icon: ChartBarDecreasing },

    {
      title: "Credits Team",
      url: "/credits-team-dashboard",
      icon: ChartBarStacked,
    },
    {
      title: "Marketer Dashboard",
      url: "/marketer-dashboard",
      icon: ChartArea,
    },

    ...(isDevelopment ? [{ title: "Credit Team", url: "credit-team", icon: DollarSign }] : []),
    ...(isDevelopment ? [{ title: "HQ Team", url: "hq-team", icon: Building }] : []),
    ...(isDevelopment ? [{ title: "Karen Team", url: "karen-team", icon: MapPin }] : []),
    ...(isDevelopment ? [{ title: "Diaspora", url: "diaspora", icon: Globe }] : []),

    { title: "Hr team", url: "/hr-team", icon: Building },
    { title: "Legal Team", url: "/legal-team", icon: Briefcase },
    { title: "DataTeam", url: "/data-team", icon: BarChart },

    {
      title: "TeleMarketing Team",
      url: "/tele-marketing-team",
      icon: PhoneCall,
    },
    { title: "Digital Team", url: "/digital-team", icon: Cpu },
  ],

  NavReports: [
    { title: "Sales Reports", url: "/reports/sales-reports", icon: ChartPie },
    { title: "Prospect Reports", url: "/money-in-reports", icon: DollarSign },
    {
      title: "Customer Reports",
      url: "/installment-reports",
      icon: CalendarCheck,
    },
    {
      title: "Customer Reports",
      url: "/customer-reports",
      icon: CalendarCheck,
    },
    { title: "Inventory Reports", url: "/project-summary", icon: FileText },
    { title: "Logistics Reports", url: "/logistics-reports", icon: Bus },
  ],

  NavAnalytics: [
    ...(isDevelopment ? [{ title: "Sales Analytics", url: "/new-sales", icon: TrendingUp }] : []),
    ...(isDevelopment ? [{
      title: "Prospect Analytics",
      url: "/money-in-Analytics",
      icon: DollarSign,
    }] : []),
    ...(isDevelopment ? [{
      title: "Customer Analytics",
      url: "/installment-Analytics",
      icon: CalendarCheck,
    }] : []),
    ...(isDevelopment ? [{ title: "Inventory Analytics", url: "/project-summary", icon: FileText }] : []),
    ...(isDevelopment ? [{ title: "Logistics Analytics", url: "/logistics-reports", icon: Bus }] : []),
  ],

  NavServices: [
    ...(isDevelopment ? [{ title: "To-Do", url: "/admin/services", icon: CheckSquare }] : []),
    ...(isDevelopment ? [{ title: "Complaints", url: "/complaints", icon: AlertCircle }] : []),
    ...(isDevelopment ? [{ title: "Feedback", url: "/feedback", icon: MessageCircle }] : []),
    {
      title: "Ticketing",
      url: "/ticketing",
      icon: Ticket,
      items: [
        { title: "Tickets", url: "/ticketing" },
        { title: "Tickets Sources", url: "/ticketing/sources" },
        { title: "Tickets Categories", url: "/ticketing/categories" },
        { title: "Tickets Logs", url: "/ticketing/logs" },
      ],
    },
    ...(isDevelopment ? [{ title: "Flags", url: "/flags", icon: Flag }] : []),
    ...(isDevelopment ? [{ title: "Engagements", url: "/engagements", icon: Users }] : []),
    ...(isDevelopment ? [{ title: "Notifications", url: "/notifications", icon: Bell }] : []),
    ...(isDevelopment ? [{ title: "Notes", url: "/notes", icon: FileText }] : []),
    ...(isDevelopment ? [{ title: "Reminders", url: "/reminders", icon: Clock }] : []),
    ...(isDevelopment ? [{ title: "Forms", url: "/forms", icon: FileEdit }] : []),
    ...(isDevelopment ? [{ title: "Surveys", url: "/surveys", icon: List }] : []),
   ],
};

export function AppSidebar(props: React.ComponentProps<typeof Sidebar>) {
  const { levelOne, setLevelOne } = useNavStore() as {
    levelOne: string | null;
    setLevelOne: (value: string | null) => void;
  };
  const [openSection, setOpenSection] = useState<string | null>("Main");
  const {
    hasSidebarAccess,
    hasLogisticsPermission,
    isLoading: loadingPermissions,
  } = useSidebarPermissions();
  // const { theme, toggleTheme } = useTheme();

  // Filter logistics navigation items based on permissions
  const filterLogisticsItems = (items: any[]) => {
    return items.filter((item) => {
      // Check specific logistics permissions
      switch (item.url) {
        case "/logistics-dash":
          return hasLogisticsPermission("ACCESS_LOGISTICS_DASHBOARD");
        case "/logistics-stats":
          return hasLogisticsPermission("ACCESS_LOGISTICS_STATISTICS");
        case "/clients":
          return hasLogisticsPermission("ACCESS_CLIENTS");
        case "/drivers":
          return hasLogisticsPermission("ACCESS_DRIVERS");
        case "/logistics/vehicles":
          return hasLogisticsPermission("ACCESS_VEHICLES");
        default:
          return true; // Allow other items by default
      }
    });
  };

  // Create filtered navigation data
  const filteredNavData = {
    ...navData,
    navMain: navData.navMain.map((item) => {
      if (item.title === "Logistics") {
        return {
          ...item,
          items: filterLogisticsItems(item.items || []),
        };
      }
      return item;
    }),
  };

  type NavDataKey = Exclude<keyof typeof navData, "teams">;
  const allSections: Array<{
    label: string;
    itemsKey: NavDataKey;
    permissionKey: SidebarPermissionKey;
  }> = [
      { label: "Main", itemsKey: "navMain", permissionKey: "MAIN" },
      ...(isDevelopment ? [{
        label: "Performance",
        itemsKey: "NavPerformance" as NavDataKey,
        permissionKey: "PERFORMANCE" as SidebarPermissionKey,
      }] : []),
      ...(isDevelopment ? [{ label: "Teams", itemsKey: "NavTeams" as NavDataKey, permissionKey: "TEAMS" as SidebarPermissionKey }] : []),
      ...(isDevelopment ? [{ label: "Reports", itemsKey: "NavReports" as NavDataKey, permissionKey: "REPORTS" as SidebarPermissionKey }] : []),
      ...(isDevelopment ? [{
        label: "Analytics",
        itemsKey: "NavAnalytics" as NavDataKey,
        permissionKey: "ANALYTICS" as SidebarPermissionKey,
      }] : []),
      { label: "Services", itemsKey: "NavServices", permissionKey: "SERVICES" },
      { label: "Administration", itemsKey: "navAdmin", permissionKey: "ADMIN" },
    ];

  // Filter sections based on user permissions
  const sections = allSections.filter((section) =>
    hasSidebarAccess(section.permissionKey)
  );

  // Fallback: If no sections are accessible, at least show Main section
  const accessibleSections = sections.length > 0 ? sections : [allSections[0]];

  return (
    <Sidebar
      collapsible="icon"
      className="bg-gray-100 text-gray-900 dark:bg-gray-900 dark:text-gray-100"
      {...props}
    >
      <SidebarHeader className="border-b border-gray-300 dark:border-gray-700 sticky top-0 z-10">
        <TeamSwitcher teams={navData.teams} />
      </SidebarHeader>

      <SidebarContent>
        {loadingPermissions ? (
          <div className="flex items-center justify-center p-4">
            <div className="text-sm text-gray-500">Loading permissions...</div>
          </div>
        ) : accessibleSections.length === 0 ? (
          <div className="flex items-center justify-center p-4">
            <div className="text-sm text-gray-500">No accessible sections</div>
          </div>
        ) : (
          accessibleSections.map(({ label, itemsKey }) => (
            <NavMain
              key={label}
              label={label}
              items={filteredNavData[itemsKey]}
              open={levelOne === label}
              onToggle={() => {
                setLevelOne(levelOne === label ? null : label)
                // setOpenSection((prev) => (prev === label ? null : label))
              }}
            />
          ))
        )}
      </SidebarContent>

      {/* <SidebarFooter className="border-t border-gray-300 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-sidebar-foreground">
            Theme
          </span>
          <div className="flex items-center space-x-2">
            <Sun className={`w-4 h-4 transition-colors duration-300 ${
              theme === "light"
                ? "text-yellow-500"
                : "text-gray-400 dark:text-gray-500"
            }`} />
            <button
              onClick={toggleTheme}
              className="
                relative inline-flex items-center h-5 w-10
                rounded-full transition-all duration-300 ease-in-out
                bg-gray-200 dark:bg-gray-600
                hover:bg-gray-300 dark:hover:bg-gray-500
                focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
                focus:ring-offset-sidebar-background
                border border-gray-300 dark:border-gray-500
              "
              aria-label={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
              title={`Switch to ${theme === "light" ? "dark" : "light"} mode`}
            >
              <span
                className={`${
                  theme === "dark" ? "translate-x-5" : "translate-x-0.5"
                } inline-block w-4 h-4 transform transition-transform duration-300 ease-in-out
                bg-white rounded-full shadow-md border border-gray-200 dark:border-gray-300`}
              />
            </button>
            <Moon className={`w-4 h-4 transition-colors duration-300 ${
              theme === "dark"
                ? "text-blue-400"
                : "text-gray-400"
            }`} />
          </div>
        </div>
      </SidebarFooter> */}
      <SidebarFooter>
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
